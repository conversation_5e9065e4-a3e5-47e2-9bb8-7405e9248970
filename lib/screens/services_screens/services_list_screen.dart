import 'package:delleny/app_config/api_providers.dart';
import 'package:delleny/app_config/app_colors.dart';
import 'package:delleny/app_config/common_components.dart';
import 'package:delleny/app_config/routes.dart';
import 'package:delleny/models/home_screens_models/services_model.dart';
import 'package:delleny/screens/drawer_screen.dart';
import 'package:delleny/screens/services_screens/service_details_screen.dart';
import 'package:delleny/widgets/services_screens_widgets/service_list_screen_widgets.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:riverpod_context/riverpod_context.dart';

class ServicesListScreen extends StatefulWidget {
  const ServicesListScreen({super.key});

  @override
  State<ServicesListScreen> createState() => _ServicesListScreenState();
}

class _ServicesListScreenState extends State<ServicesListScreen> {
  final TextEditingController _searchController = TextEditingController();
  Future<List<ServiceModel>>? _servicesFuture;

  @override
  void initState() {
    super.initState();
    // Use WidgetsBinding to safely access context after initState
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read(ApiProviders.homeServicesProvider).setIsSearch(false);
        _loadServices();
      }
    });
  }

  void _loadServices() {
    _servicesFuture =
        context.read(ApiProviders.homeServicesProvider).getAllServices(
              context: context,
              search: context.read(ApiProviders.homeServicesProvider).isSearch
                  ? _searchController.text
                  : "",
            );
  }
  // @override
  // void initState() {
  //   super.initState();
  //   // Use WidgetsBinding to safely access context after initState
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     if (mounted) {
  //       context.read(ApiProviders.homeServicesProvider).setIsSearch(false);
  //     }
  //   });
  // }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;

    return Scaffold(
      drawer: const DrawerScreen(path: PATHS.servicesListScreen),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.pushNamed(context, PATHS.addServiceScreen);
        },
        foregroundColor: Colors.white,
        backgroundColor: AppColors.orangeColor,
        child: const Icon(Icons.add),
      ),
      appBar: CommonComponents.commonAppBar(
          title: "services_list", context: context),
      body: Consumer(
        builder: (context, watch, child) => FutureBuilder(
            key: watch.watch(ApiProviders.homeServicesProvider).servicesKey,
            // future: _servicesFuture,
            future: context
                .read(ApiProviders.homeServicesProvider)
                .getAllServices(
                    context: context,
                    search:
                        watch.watch(ApiProviders.homeServicesProvider).isSearch
                            ? _searchController.text
                            : ""),
            builder: (context, AsyncSnapshot<List<ServiceModel>> snapshot) {
              if (snapshot.data == null) {
                return CommonComponents.loadingDataFromServer();
              } else {
                return Padding(
                  padding: EdgeInsets.all(10.0.h),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _searchController,
                        onFieldSubmitted: (value) async {
                          try {
                            if (mounted && value.length >= 2) {
                              if (value.isNotEmpty) {
                                context
                                    .read(ApiProviders.homeServicesProvider)
                                    .setIsSearch(true);
                              } else {
                                context
                                    .read(ApiProviders.homeServicesProvider)
                                    .setIsSearch(false);
                              }

                              if (mounted) {
                                context
                                    .read(ApiProviders.homeServicesProvider)
                                    .rebuildServices();
                              }
                            } else {
                              if (mounted) {
                                CommonComponents.showCustomizedSnackBar(
                                    context: context,
                                    title:
                                        "please_enter_at_least_two_characters");
                              }
                            }
                          } catch (e) {
                            debugPrint("Search error: $e");
                            if (mounted) {
                              CommonComponents.showCustomizedSnackBar(
                                  context: context, title: "search_error");
                            }
                          }
                        },
                        decoration: InputDecoration(
                          hintText: "what_are_you_looking_for?".tr(),
                          hintStyle: TextStyle(
                              fontSize: 12.0.sp, color: AppColors.greyColor),
                          isDense: true,
                          prefixIcon: const Icon(Icons.search),
                          prefixIconColor: AppColors.greyColor,
                          prefixIconConstraints: BoxConstraints(
                              minHeight: 15.0.h, minWidth: 30.0.w),
                          border: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                          disabledBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderSide:
                                const BorderSide(color: AppColors.greyColor),
                            borderRadius: BorderRadius.circular(10.0.r),
                          ),
                        ),
                      ),
                      SizedBox(height: 20.0.h),
                      Expanded(
                        child: ListView.separated(
                          separatorBuilder: (context, index) =>
                              SizedBox(height: 10.0.h),
                          itemCount: snapshot.data!.length,
                          itemBuilder: (context, index) => InkWell(
                            onTap: () {
                              Navigator.pushNamed(
                                  context, PATHS.servicesDetailsScreen,
                                  arguments: ServicesDetailsScreen(
                                    serviceDetails: snapshot.data![index],
                                  ));
                            },
                            child: Card(
                              child: Padding(
                                padding: EdgeInsets.all(10.0.h),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        FadeInImage.assetNetwork(
                                          placeholder:
                                              CommonComponents.placeHolderImage,
                                          image: snapshot.data![index].image!,
                                          imageCacheHeight:
                                              (100.0.h * devicePixelRatio)
                                                  .round(),
                                          imageCacheWidth:
                                              (110.0.w * devicePixelRatio)
                                                  .round(),
                                          placeholderCacheHeight:
                                              (100.0.h * devicePixelRatio)
                                                  .round(),
                                          placeholderCacheWidth:
                                              (110.0.w * devicePixelRatio)
                                                  .round(),
                                          height: 100.0.h,
                                          width: 110.0.w,
                                          fit: BoxFit.fill,
                                        ),
                                        SizedBox(width: 10.0.w),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Text(
                                                      snapshot
                                                          .data![index].name!,
                                                      style: TextStyle(
                                                        fontSize: 16.0.sp,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                      maxLines: 2,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                  IconButton(
                                                    onPressed: () {
                                                      watch
                                                          .watch(ApiProviders
                                                              .homeServicesProvider)
                                                          .addServiceToFavourites(
                                                              context,
                                                              snapshot.data![
                                                                  index]);
                                                    },
                                                    icon: snapshot
                                                            .data![index].isFav!
                                                        ? const Icon(
                                                            Icons.favorite)
                                                        : const Icon(Icons
                                                            .favorite_border),
                                                    iconSize: 20.0.h,
                                                    color: snapshot
                                                            .data![index].isFav!
                                                        ? AppColors.orangeColor
                                                        : AppColors.greyColor,
                                                  ),
                                                ],
                                              ),
                                              RatingBar(
                                                ratingWidget: RatingWidget(
                                                    full: const Icon(Icons.star,
                                                        color: AppColors
                                                            .orangeColor),
                                                    half: const Icon(
                                                      Icons.star_half_outlined,
                                                      color:
                                                          AppColors.orangeColor,
                                                    ),
                                                    empty: const Icon(
                                                        Icons.star_border,
                                                        color: AppColors
                                                            .greyColor)),
                                                itemSize: 20.0.sp,
                                                ignoreGestures: true,
                                                initialRating: double.parse(
                                                    snapshot.data![index].rate!
                                                        .toString()),
                                                allowHalfRating: true,
                                                onRatingUpdate: (value) {},
                                              ),
                                              SizedBox(height: 10.0.h),
                                              Row(
                                                children: [
                                                  Icon(Icons.location_on,
                                                      color:
                                                          AppColors.greyColor,
                                                      size: 12.0.h),
                                                  SizedBox(width: 5.0.w),
                                                  Expanded(
                                                    child: Text(
                                                      snapshot.data![index]
                                                          .address!,
                                                      style: TextStyle(
                                                        fontSize: 12.0.sp,
                                                        color:
                                                            AppColors.greyColor,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                    SizedBox(height: 10.0.h),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        ServicesListScreenWidgets
                                            .servicesListButtonsWidget(
                                          icon: Icons.directions,
                                          title: "directions",
                                          onPress: () async {
                                            if (snapshot
                                                    .data![index].mapAddress !=
                                                null) {
                                              await CommonComponents
                                                  .launchOnBrowser(
                                                url: snapshot
                                                    .data![index].mapAddress!,
                                                context: context,
                                              );
                                            } else {
                                              CommonComponents
                                                  .showCustomizedSnackBar(
                                                context: context,
                                                title: "location_not_found",
                                              );
                                            }
                                          },
                                        ),
                                        ServicesListScreenWidgets
                                            .servicesListButtonsWidget(
                                          icon: Icons.phone,
                                          title: "call",
                                          onPress: () async {
                                            if (snapshot.data![index].phone !=
                                                null) {
                                              await CommonComponents
                                                  .launchOnBrowser(
                                                url:
                                                    "tel:${snapshot.data![index].phone}",
                                                context: context,
                                              );
                                            } else {
                                              CommonComponents
                                                  .showCustomizedSnackBar(
                                                context: context,
                                                title: "phone_number_not_found",
                                              );
                                            }
                                          },
                                        ),
                                        ServicesListScreenWidgets
                                            .servicesListButtonsWidget(
                                          icon: FontAwesomeIcons.whatsapp,
                                          title: "Whats App",
                                          onPress: () async {
                                            if (snapshot.data![index]
                                                    .whatsAppNumber !=
                                                null) {
                                              await CommonComponents
                                                  .launchOnBrowser(
                                                url:
                                                    "https://api.whatsapp.com/send?phone=2${snapshot.data![index].whatsAppNumber}",
                                                context: context,
                                              );
                                            } else {
                                              CommonComponents
                                                  .showCustomizedSnackBar(
                                                context: context,
                                                title:
                                                    "whats_up_number_not_found",
                                              );
                                            }
                                          },
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                );
              }
            }),
      ),
    );
  }
}
